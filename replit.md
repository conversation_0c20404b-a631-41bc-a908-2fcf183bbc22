# Amazon Product Scraper

## Overview

A Streamlit web application that extracts product details from Amazon URLs. The application takes an Amazon product URL as input and scrapes key product information including name, price, and images. It features URL validation for multiple Amazon domains and provides a user-friendly interface for product data extraction.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Streamlit Framework**: Web interface built with Streamlit for rapid development and deployment
- **Wide Layout Configuration**: Uses wide page layout to accommodate product images and details
- **Interactive Components**: Text input fields, buttons, and validation feedback
- **Real-time Validation**: Client-side URL validation before processing

### Backend Architecture
- **Modular Design**: Separated scraping logic into dedicated `scraper.py` module
- **Session Management**: Persistent HTTP session for efficient request handling
- **Browser Mimicking**: Comprehensive headers to simulate real browser requests
- **Error Handling**: Structured error responses with success/failure status

### Data Processing
- **BeautifulSoup Parser**: HTML parsing for product data extraction
- **Regular Expression Validation**: URL pattern matching for Amazon domains
- **Image Processing**: PIL integration for image handling and display

### Anti-Detection Measures
- **Random Delays**: Implements random wait times between requests
- **Realistic Headers**: Full browser header simulation including security headers
- **Session Persistence**: Maintains session state to appear as continuous browsing

## External Dependencies

### Core Libraries
- **Streamlit**: Web application framework for the user interface
- **Requests**: HTTP library for web scraping and API calls
- **BeautifulSoup4**: HTML/XML parsing library for content extraction
- **PIL (Pillow)**: Image processing library for handling product images

### Supported Platforms
- **Amazon Global Domains**: Supports 12 Amazon regional domains (com, co.uk, de, fr, it, es, ca, com.au, co.jp, in, com.br, com.mx)
- **HTTP/HTTPS Protocols**: Handles both secure and non-secure Amazon URLs

### Browser Simulation
- **User Agent Spoofing**: Chrome browser simulation for request authenticity
- **Security Headers**: Modern browser security headers (Sec-Fetch-*) implementation
- **Content Negotiation**: Proper Accept headers for HTML, images, and other content types
import requests
from bs4 import BeautifulSoup
import re
import time
import random
from urllib.parse import urljoin

class AmazonScraper:
    def __init__(self):
        self.session = requests.Session()
        
        # Headers to mimic a real browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        
        self.session.headers.update(self.headers)

    def scrape_product(self, url):
        """
        Scrape product details from Amazon URL
        Returns dict with success status and product data
        """
        try:
            # Add random delay to avoid being blocked
            time.sleep(random.uniform(1, 3))
            
            # Make request
            response = self.session.get(url, timeout=15)
            
            if response.status_code != 200:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Unable to access the page'
                }
            
            # Parse HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract product details
            product_data = {
                'name': self._extract_product_name(soup),
                'price': self._extract_price(soup),
                'image_url': self._extract_image_url(soup, url)
            }
            
            return {
                'success': True,
                'name': product_data['name'],
                'price': product_data['price'],
                'image_url': product_data['image_url']
            }
            
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': 'Request timed out. Please try again.'
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': 'Connection error. Please check your internet connection.'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}'
            }

    def _extract_product_name(self, soup):
        """Extract product name from various possible selectors"""
        name_selectors = [
            '#productTitle',
            '.product-title',
            '[data-automation-id="product-title"]',
            '.a-size-large.product-title-word-break',
            '.a-size-large',
            'h1.a-size-large',
            'h1[data-automation-id="product-title"]'
        ]
        
        for selector in name_selectors:
            try:
                element = soup.select_one(selector)
                if element:
                    name = element.get_text(strip=True)
                    if name and len(name) > 10:  # Basic validation
                        return name
            except:
                continue
        
        # Fallback: try to find title in page title
        try:
            page_title = soup.find('title')
            if page_title:
                title_text = page_title.get_text()
                # Extract product name from title (usually before " : Amazon")
                if ':' in title_text:
                    potential_name = title_text.split(':')[0].strip()
                    if len(potential_name) > 10:
                        return potential_name
        except:
            pass
        
        return None

    def _extract_price(self, soup):
        """Extract price from various possible selectors"""
        price_selectors = [
            '.a-price.a-text-price.a-size-medium.apexPriceToPay .a-offscreen',
            '.a-price-current .a-offscreen',
            '.a-price .a-offscreen',
            '.a-price-symbol + .a-price-fraction',
            '#priceblock_dealprice',
            '#priceblock_ourprice',
            '#listPrice',
            '.a-size-base.a-color-price',
            '.a-price-range',
            '[data-automation-id="product-price"] .a-price .a-offscreen',
            '.a-price-whole'
        ]
        
        for selector in price_selectors:
            try:
                element = soup.select_one(selector)
                if element:
                    price_text = element.get_text(strip=True)
                    if price_text and ('$' in price_text or '£' in price_text or '€' in price_text or '¥' in price_text or '₹' in price_text or 'Rs' in price_text):
                        return price_text
            except:
                continue
        
        # Try to find price in script tags (JSON data)
        try:
            script_tags = soup.find_all('script')
            for script in script_tags:
                if script.string and 'price' in script.string.lower():
                    # Look for price patterns in script content
                    price_pattern = r'["\']price["\']:\s*["\']([^"\']+)["\']'
                    match = re.search(price_pattern, script.string, re.IGNORECASE)
                    if match:
                        price = match.group(1)
                        if '$' in price or '£' in price or '€' in price or '₹' in price or 'Rs' in price:
                            return price
        except:
            pass
        
        return None

    def _extract_image_url(self, soup, base_url):
        """Extract main product image URL"""
        image_selectors = [
            '#landingImage',
            '#imgTagWrapperId img',
            '.a-dynamic-image',
            '[data-automation-id="product-image"] img',
            '.imgTagWrapper img',
            '#main-image-container img',
            '.s-image'
        ]
        
        for selector in image_selectors:
            try:
                img_element = soup.select_one(selector)
                if img_element:
                    # Try different attributes
                    for attr in ['data-old-hires', 'data-src', 'src']:
                        img_url = img_element.get(attr)
                        if img_url:
                            # Make URL absolute if needed
                            if img_url.startswith('//'):
                                img_url = 'https:' + img_url
                            elif img_url.startswith('/'):
                                img_url = urljoin(base_url, img_url)
                            
                            # Validate it's an image URL
                            if any(ext in img_url.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
                                return img_url
            except:
                continue
        
        # Try to find image in JSON-LD structured data
        try:
            script_tags = soup.find_all('script', type='application/ld+json')
            for script in script_tags:
                if script.string:
                    import json
                    try:
                        data = json.loads(script.string)
                        if isinstance(data, dict) and 'image' in data:
                            image = data['image']
                            if isinstance(image, str):
                                return image
                            elif isinstance(image, list) and len(image) > 0:
                                return image[0]
                    except:
                        continue
        except:
            pass
        
        return None

modules = ["python-3.11"]

[nix]
channel = "stable-25_05"
packages = ["freetype", "lcms2", "libimagequant", "libjpeg", "libtiff", "libwebp", "libxcrypt", "openjpeg", "tcl", "tk", "zlib"]

[deployment]
deploymentTarget = "autoscale"
run = ["streamlit", "run", "app.py", "--server.port", "5000"]

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Streamlit App"

[[workflows.workflow]]
name = "Streamlit App"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "streamlit run app.py --server.port 5000"
waitForPort = 5000

[workflows.workflow.metadata]
outputType = "webview"

[[ports]]
localPort = 5000
externalPort = 80

[agent]
integrations = ["web_scraper==1.0.0"]

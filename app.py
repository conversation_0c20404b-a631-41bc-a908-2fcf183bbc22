import streamlit as st
import re
from scraper import Amazon<PERSON>craper
from PIL import Image
import requests
from io import BytesIO
import datetime
import uuid

# Configure page
st.set_page_config(
    page_title="My Wishlist - Wish Fetcher",
    page_icon="💝",
    layout="wide"
)

# Initialize scraper
scraper = AmazonScraper()

# Initialize session state for wishlist
if 'wishlist' not in st.session_state:
    st.session_state.wishlist = []
if 'search_query' not in st.session_state:
    st.session_state.search_query = ''
if 'filter_priority' not in st.session_state:
    st.session_state.filter_priority = 'All Items'

# Custom CSS for modern, clean styling
st.markdown("""
<style>
/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Global Styles */
.main .block-container {
    padding-top: 2rem;
    padding-bottom: 2rem;
    max-width: 1200px;
}

/* Color Variables */
:root {
    --primary-color: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-light: #f8fafc;
    --background-white: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-light: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Typography */
.main {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Header Styling */
.wishlist-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    padding: 3rem 2rem;
    border-radius: 20px;
    color: white;
    text-align: center;
    margin-bottom: 2.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.wishlist-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
}

.wishlist-header p {
    font-size: 1.125rem;
    opacity: 0.9;
    font-weight: 400;
    margin-bottom: 0;
}

.wishlist-stats {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.stat-badge {
    background: rgba(255, 255, 255, 0.15);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 500;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
}

/* Product Cards */
.product-card {
    border: 1px solid var(--border-light);
    border-radius: 16px;
    padding: 1.5rem;
    margin: 0.75rem 0;
    background: var(--background-white);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

.product-card:hover::before {
    opacity: 1;
}

/* Priority Badges */
.priority-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.75rem;
}

.high-priority {
    background-color: #fef2f2;
    color: var(--error-color);
    border: 1px solid #fecaca;
}

.medium-priority {
    background-color: #fffbeb;
    color: var(--warning-color);
    border: 1px solid #fed7aa;
}

.low-priority {
    background-color: #f0fdf4;
    color: var(--success-color);
    border: 1px solid #bbf7d0;
}

/* Form Styling */
.stTextInput > div > div > input {
    border-radius: 12px;
    border: 2px solid var(--border-light);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
    background-color: var(--background-white);
}

.stTextInput > div > div > input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.stSelectbox > div > div > div {
    border-radius: 12px;
    border: 2px solid var(--border-light);
    background-color: var(--background-white);
}

/* Button Styling */
.stButton > button {
    border-radius: 12px;
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
    letter-spacing: 0.025em;
}

.stButton > button[kind="primary"] {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow-sm);
}

.stButton > button[kind="primary"]:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.stButton > button[kind="secondary"] {
    background: var(--background-white);
    color: var(--text-primary);
    border: 2px solid var(--border-light);
}

.stButton > button[kind="secondary"]:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
}

/* Expander Styling */
.streamlit-expanderHeader {
    border-radius: 12px;
    background-color: var(--background-light);
    border: 1px solid var(--border-light);
    padding: 1rem;
    font-weight: 500;
}

.streamlit-expanderContent {
    border-radius: 0 0 12px 12px;
    border: 1px solid var(--border-light);
    border-top: none;
    background-color: var(--background-white);
    padding: 1.5rem;
}

/* Divider */
hr {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-light), transparent);
    margin: 2rem 0;
}

/* Link Button Styling */
.stLinkButton > a {
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
    display: inline-block;
    text-align: center;
    background: var(--primary-color);
    color: white;
    border: none;
}

.stLinkButton > a:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 768px) {
    .wishlist-header {
        padding: 2rem 1rem;
    }

    .wishlist-header h1 {
        font-size: 2rem;
    }

    .wishlist-stats {
        gap: 1rem;
    }

    .stat-badge {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }

    .product-card {
        padding: 1rem;
        margin: 0.5rem 0;
    }
}
</style>
""", unsafe_allow_html=True)

# Wishlist Header
total_items = len(st.session_state.wishlist)
high_priority_items = len([item for item in st.session_state.wishlist if item.get('priority') == 'High'])
total_value = sum([float(item.get('price', '0').replace('₹', '').replace(',', '').replace('$', '').split()[0]) if item.get('price') and any(c.isdigit() for c in item.get('price', '')) else 0 for item in st.session_state.wishlist])

st.markdown(f"""
<div class="wishlist-header">
    <h1>🛍️ My Wishlist</h1>
    <p>Curate your perfect collection of desired items from around the web</p>
    <div class="wishlist-stats">
        <div class="stat-badge">📦 {total_items} Items</div>
        <div class="stat-badge">🔥 {high_priority_items} High Priority</div>
        <div class="stat-badge">💰 ₹{total_value:,.0f} Total Value</div>
    </div>
</div>
""", unsafe_allow_html=True)

# Search and filter section
st.markdown("### 🔍 Search & Filter")
col1, col2, col3, col4 = st.columns([3, 2, 2, 1])

with col1:
    search_query = st.text_input(
        "Search",
        placeholder="Search your wishlist by product name...",
        key="search_input",
        help="Type to search through your saved products"
    )

with col2:
    filter_priority = st.selectbox(
        "Filter by Priority",
        ["All Items", "High Priority", "Medium Priority", "Low Priority"],
        key="priority_filter",
        help="Filter products by priority level"
    )

with col3:
    sort_by = st.selectbox(
        "Sort By",
        ["Date Added (Newest)", "Date Added (Oldest)", "Priority (High to Low)", "Priority (Low to High)", "Name (A-Z)", "Name (Z-A)"],
        help="Choose how to sort your products"
    )

with col4:
    view_mode = st.selectbox(
        "View Mode",
        ["Grid View 🔲", "List View 📝"],
        help="Switch between grid and list view"
    )

st.markdown("---")

# Add New Product Section
with st.expander("➕ Add New Product to Your Wishlist", expanded=False):
    st.markdown("### 🔗 Add from URL")
    st.markdown("Paste a product URL from Amazon, Flipkart, or any e-commerce website to automatically fetch product details.")

    col1, col2 = st.columns([4, 1])

    with col1:
        url_input = st.text_input(
            "Product URL",
            placeholder="https://www.amazon.in/product-url or any e-commerce URL",
            help="Paste a product URL from any e-commerce website to automatically fetch details"
        )

    with col2:
        st.markdown("<br>", unsafe_allow_html=True)  # Add spacing to align with input
        fetch_button = st.button("🔍 Fetch Details", type="primary", use_container_width=True)

    # Priority selection
    col_priority, col_spacer = st.columns([2, 2])
    with col_priority:
        priority = st.selectbox(
            "Set Priority Level",
            ["Medium Priority", "High Priority", "Low Priority"],
            index=0,
            help="Choose the priority level for this item"
        )

    st.markdown("---")
    st.markdown("### ✏️ Add Manually")
    st.markdown("Prefer to add product details manually? Use the form below.")

    # Manual add form - always visible
    with st.form("manual_add_form", clear_on_submit=True):
        col1, col2 = st.columns(2)

        with col1:
            manual_name = st.text_input(
                "Product Name *",
                placeholder="Enter product name",
                help="Required field"
            )
            manual_price = st.text_input(
                "Price",
                placeholder="₹1,999 or $29.99",
                help="Enter price with currency symbol"
            )

        with col2:
            manual_image = st.text_input(
                "Image URL (Optional)",
                placeholder="https://example.com/image.jpg",
                help="Direct link to product image"
            )
            manual_priority = st.selectbox(
                "Priority Level",
                ["Medium Priority", "High Priority", "Low Priority"],
                help="Set the priority for this item"
            )

        col_submit, col_spacer = st.columns([1, 3])
        with col_submit:
            submit_manual = st.form_submit_button("✅ Add to Wishlist", type="primary", use_container_width=True)

        if submit_manual:
            if manual_name.strip():
                new_item = {
                    'id': str(uuid.uuid4()),
                    'name': manual_name.strip(),
                    'price': manual_price.strip() if manual_price.strip() else 'Price not available',
                    'image_url': manual_image.strip() if manual_image.strip() else None,
                    'url': '',
                    'priority': manual_priority.split()[0],
                    'date_added': datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
                }
                st.session_state.wishlist.append(new_item)
                st.success(f"✅ Successfully added '{manual_name}' to your wishlist!")
                st.rerun()
            else:
                st.error("⚠️ Please enter a product name.")

# Validate URL function
def is_valid_url(url):
    """Validate if the URL is valid"""
    amazon_domains = [
        'amazon.com', 'amazon.co.uk', 'amazon.de', 'amazon.fr', 
        'amazon.it', 'amazon.es', 'amazon.ca', 'amazon.com.au',
        'amazon.co.jp', 'amazon.in', 'amazon.com.br', 'amazon.com.mx'
    ]
    
    # Check for Amazon URLs
    amazon_pattern = r'https?://(www\.)?(' + '|'.join(amazon_domains) + ')/.*/dp/[A-Z0-9]{10}'
    if re.match(amazon_pattern, url):
        return True
    
    # Basic URL validation for other e-commerce sites
    general_pattern = r'https?://[^\s/$.?#].[^\s]*'
    return re.match(general_pattern, url) is not None

# Process URL when fetch button is clicked
if fetch_button:
    if not url_input:
        st.error("Please enter a URL first.")
    elif not is_valid_url(url_input):
        st.error("Please enter a valid product URL.")
    else:
        # Show loading spinner
        with st.spinner("Fetching product details... This may take a few seconds."):
            try:
                # Try to scrape product details
                product_data = scraper.scrape_product(url_input)
                
                if product_data['success']:
                    # Add to wishlist
                    new_item = {
                        'id': str(uuid.uuid4()),
                        'name': product_data['name'] or 'Product name not found',
                        'price': product_data['price'] or 'Price not available',
                        'image_url': product_data['image_url'],
                        'url': url_input,
                        'priority': priority.split()[0],  # Extract High/Medium/Low
                        'date_added': datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
                    }
                    
                    st.session_state.wishlist.append(new_item)
                    st.success(f"Added '{new_item['name'][:50]}...' to your wishlist!")
                    st.rerun()
                
                else:
                    st.error(f"Could not fetch product details: {product_data['error']}")
                    st.info("You can still add this product manually using the form below.")
                    
            except Exception as e:
                st.error(f"An error occurred while fetching: {str(e)}")
                st.info("You can still add this product manually using the form below.")

def display_product_card(item):
    """Display product as a modern card with improved styling"""
    priority_class = f"{item['priority'].lower()}-priority"

    # Create card HTML structure
    card_html = f"""
    <div class="product-card">
        <div class="priority-badge {priority_class}">
            {item['priority']} Priority
        </div>
    """

    st.markdown(card_html, unsafe_allow_html=True)

    with st.container():
        col_img, col_content = st.columns([1, 2])

        with col_img:
            # Product image with better styling
            if item['image_url']:
                try:
                    response = requests.get(item['image_url'], timeout=10)
                    if response.status_code == 200:
                        image = Image.open(BytesIO(response.content))
                        st.image(image, width=180, use_column_width=False)
                    else:
                        st.image("https://via.placeholder.com/180x180/f8fafc/64748b?text=No+Image", width=180)
                except:
                    st.image("https://via.placeholder.com/180x180/f8fafc/64748b?text=No+Image", width=180)
            else:
                st.image("https://via.placeholder.com/180x180/f8fafc/64748b?text=No+Image", width=180)

        with col_content:
            # Product name with better typography
            st.markdown(f"### {item['name'][:60]}{'...' if len(item['name']) > 60 else ''}")

            # Price with better styling
            if item['price'] and item['price'] != 'Price not available':
                st.markdown(f"**💰 {item['price']}**")
            else:
                st.markdown("**💰 Price not available**")

            # Date added with icon
            st.caption(f"📅 Added on {item['date_added']}")

            # Action buttons with better spacing
            st.markdown("<br>", unsafe_allow_html=True)
            col_view, col_edit, col_remove = st.columns(3)

            with col_view:
                if item['url']:
                    st.link_button("🔗 View Product", item['url'], use_container_width=True)
                else:
                    st.button("🔗 View Product", disabled=True, use_container_width=True, help="No URL available")

            with col_edit:
                if st.button("✏️ Edit", key=f"edit_{item['id']}", use_container_width=True, help="Edit product details"):
                    st.info("🚧 Edit functionality coming soon!")

            with col_remove:
                if st.button("🗑️ Remove", key=f"remove_{item['id']}", use_container_width=True, help="Remove from wishlist"):
                    st.session_state.wishlist = [i for i in st.session_state.wishlist if i['id'] != item['id']]
                    st.success(f"Removed '{item['name'][:30]}...' from wishlist")
                    st.rerun()

    st.markdown("</div>", unsafe_allow_html=True)
    st.markdown("<br>", unsafe_allow_html=True)

def display_product_list_item(item):
    """Display product as a clean list item"""
    priority_class = f"{item['priority'].lower()}-priority"

    # Create a container with better styling
    with st.container():
        col_img, col_content, col_price, col_actions = st.columns([1, 5, 2, 2])

        with col_img:
            # Compact product image
            if item['image_url']:
                try:
                    response = requests.get(item['image_url'], timeout=10)
                    if response.status_code == 200:
                        image = Image.open(BytesIO(response.content))
                        st.image(image, width=80)
                    else:
                        st.image("https://via.placeholder.com/80x80/f8fafc/64748b?text=No+Image", width=80)
                except:
                    st.image("https://via.placeholder.com/80x80/f8fafc/64748b?text=No+Image", width=80)
            else:
                st.image("https://via.placeholder.com/80x80/f8fafc/64748b?text=No+Image", width=80)

        with col_content:
            # Priority badge (inline)
            priority_colors = {"High": "🔴", "Medium": "🟡", "Low": "🟢"}
            priority_emoji = priority_colors.get(item['priority'], '⚪')

            # Product name with priority
            st.markdown(f"**{item['name'][:80]}{'...' if len(item['name']) > 80 else ''}**")
            st.markdown(f"{priority_emoji} {item['priority']} Priority • 📅 {item['date_added']}")

        with col_price:
            if item['price'] and item['price'] != 'Price not available':
                st.markdown(f"**💰 {item['price']}**")
            else:
                st.markdown("**💰 N/A**")

        with col_actions:
            if item['url']:
                st.link_button("🔗 View", item['url'], use_container_width=True)
            else:
                st.button("🔗 View", disabled=True, use_container_width=True)

            if st.button("🗑️", key=f"remove_list_{item['id']}", use_container_width=True, help="Remove from wishlist"):
                st.session_state.wishlist = [i for i in st.session_state.wishlist if i['id'] != item['id']]
                st.success(f"Removed '{item['name'][:30]}...' from wishlist")
                st.rerun()

    st.markdown("---")

# Display Wishlist
st.markdown("## 🛍️ Your Wishlist")

if not st.session_state.wishlist:
    # Empty state with better styling
    st.markdown("""
    <div style="text-align: center; padding: 3rem; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: 16px; margin: 2rem 0;">
        <h3 style="color: #64748b; margin-bottom: 1rem;">🛒 Your wishlist is empty</h3>
        <p style="color: #64748b; margin-bottom: 0;">Start building your dream collection by adding products above!</p>
    </div>
    """, unsafe_allow_html=True)
else:
    # Filter and search logic
    filtered_items = st.session_state.wishlist.copy()

    # Apply search filter
    if search_query:
        filtered_items = [item for item in filtered_items if
                         search_query.lower() in item['name'].lower()]

    # Apply priority filter
    if filter_priority != "All Items":
        priority_key = filter_priority.split()[0]  # Extract High/Medium/Low
        filtered_items = [item for item in filtered_items if
                         item['priority'] == priority_key]

    # Apply enhanced sorting
    if sort_by == "Date Added (Newest)":
        filtered_items.sort(key=lambda x: x['date_added'], reverse=True)
    elif sort_by == "Date Added (Oldest)":
        filtered_items.sort(key=lambda x: x['date_added'], reverse=False)
    elif sort_by == "Priority (High to Low)":
        priority_order = {"High": 3, "Medium": 2, "Low": 1}
        filtered_items.sort(key=lambda x: priority_order.get(x['priority'], 0), reverse=True)
    elif sort_by == "Priority (Low to High)":
        priority_order = {"High": 3, "Medium": 2, "Low": 1}
        filtered_items.sort(key=lambda x: priority_order.get(x['priority'], 0), reverse=False)
    elif sort_by == "Name (A-Z)":
        filtered_items.sort(key=lambda x: x['name'].lower())
    elif sort_by == "Name (Z-A)":
        filtered_items.sort(key=lambda x: x['name'].lower(), reverse=True)

    if not filtered_items:
        st.markdown("""
        <div style="text-align: center; padding: 2rem; background: #fef2f2; border-radius: 12px; border: 1px solid #fecaca;">
            <p style="color: #dc2626; margin: 0;">🔍 No items match your search criteria. Try adjusting your filters.</p>
        </div>
        """, unsafe_allow_html=True)
    else:
        # Show results count
        st.markdown(f"**Showing {len(filtered_items)} of {len(st.session_state.wishlist)} items**")

        # Display items based on view mode
        if view_mode == "Grid View 🔲":  # Grid view
            # Use 2 columns for better spacing on larger screens
            cols = st.columns(2)
            for idx, item in enumerate(filtered_items):
                with cols[idx % 2]:
                    display_product_card(item)
        else:  # List view
            # Add header for list view
            col_h1, col_h2, col_h3, col_h4 = st.columns([1, 5, 2, 2])
            with col_h1:
                st.markdown("**Image**")
            with col_h2:
                st.markdown("**Product Details**")
            with col_h3:
                st.markdown("**Price**")
            with col_h4:
                st.markdown("**Actions**")
            st.markdown("---")

            for item in filtered_items:
                display_product_list_item(item)

# Enhanced Footer
st.markdown("---")
st.markdown("""
<div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: 16px; margin-top: 2rem;">
    <h4 style="color: #475569; margin-bottom: 0.5rem;">🛍️ Wishlist Manager</h4>
    <p style="color: #64748b; margin: 0; font-size: 0.9rem;">
        Curate your perfect collection of desired items from any e-commerce website.<br>
        Built with ❤️ using Streamlit • Support for Amazon, Flipkart, and more
    </p>
</div>
""", unsafe_allow_html=True)

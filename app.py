import streamlit as st
import re
from scraper import AmazonScraper
from PIL import Image
import requests
from io import BytesIO
import datetime
import uuid

# Configure page
st.set_page_config(
    page_title="My Wishlist - Wish Fetcher",
    page_icon="💝",
    layout="wide"
)

# Initialize scraper
scraper = AmazonScraper()

# Initialize session state for wishlist
if 'wishlist' not in st.session_state:
    st.session_state.wishlist = []
if 'search_query' not in st.session_state:
    st.session_state.search_query = ''
if 'filter_priority' not in st.session_state:
    st.session_state.filter_priority = 'All Items'

# Custom CSS for styling
st.markdown("""
<style>
.wishlist-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;
    border-radius: 15px;
    color: white;
    text-align: center;
    margin-bottom: 2rem;
}
.wishlist-stats {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}
.stat-badge {
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}
.product-card {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 1rem;
    margin: 0.5rem;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}
.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}
.priority-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    color: white;
}
.high-priority { background-color: #ff4757; }
.medium-priority { background-color: #ffa726; }
.low-priority { background-color: #26c6da; }
</style>
""", unsafe_allow_html=True)

# Wishlist Header
st.markdown("""
<div class="wishlist-header">
    <h1>💝 My Wishlist</h1>
    <p>Curate your perfect collection of desired items</p>
    <div class="wishlist-stats">
        <div class="stat-badge">{} Items</div>
        <div class="stat-badge">{} Priority</div>
    </div>
</div>
""".format(
    len(st.session_state.wishlist),
    len([item for item in st.session_state.wishlist if item.get('priority') == 'High'])
), unsafe_allow_html=True)

# Search and filter row
col1, col2, col3, col4 = st.columns([3, 2, 2, 1])

with col1:
    search_query = st.text_input(
        "🔍", 
        placeholder="Search your wishlist...",
        key="search_input",
        label_visibility="collapsed"
    )

with col2:
    filter_priority = st.selectbox(
        "Priority Filter",
        ["All Items", "High Priority", "Medium Priority", "Low Priority"],
        key="priority_filter",
        label_visibility="collapsed"
    )

with col3:
    sort_by = st.selectbox(
        "Sort By", 
        ["Date Added", "Priority", "Price (Low to High)", "Price (High to Low)"],
        label_visibility="collapsed"
    )

with col4:
    view_mode = st.selectbox(
        "View",
        ["🔲", "📝"],
        label_visibility="collapsed"
    )

st.markdown("---")

# Add New Product Section
with st.expander("➕ Add New Product", expanded=False):
    st.markdown("**Product URL**")
    
    col1, col2 = st.columns([4, 1])
    
    with col1:
        url_input = st.text_input(
            "URL",
            placeholder="https://www.amazon.in/product-url or any e-commerce URL",
            help="Paste a product URL from any e-commerce website to automatically fetch details",
            label_visibility="collapsed"
        )
    
    with col2:
        fetch_button = st.button("Fetch", type="primary", use_container_width=True)
    
    # Priority selection
    priority = st.selectbox(
        "Priority Level",
        ["High Priority", "Medium Priority", "Low Priority"],
        index=1
    )
    
    st.markdown("<div style='text-align: center; margin: 1rem 0;'>Or add manually</div>", unsafe_allow_html=True)
    
    if st.button("➕ Add Manually", use_container_width=True):
        # Manual add form
        with st.form("manual_add_form"):
            manual_name = st.text_input("Product Name")
            manual_price = st.text_input("Price")
            manual_image = st.text_input("Image URL (optional)")
            manual_priority = st.selectbox("Priority", ["High Priority", "Medium Priority", "Low Priority"])
            
            if st.form_submit_button("Add to Wishlist"):
                if manual_name:
                    new_item = {
                        'id': str(uuid.uuid4()),
                        'name': manual_name,
                        'price': manual_price or 'Price not available',
                        'image_url': manual_image,
                        'url': '',
                        'priority': manual_priority.split()[0],
                        'date_added': datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
                    }
                    st.session_state.wishlist.append(new_item)
                    st.success(f"Added '{manual_name}' to your wishlist!")
                    st.rerun()
                else:
                    st.error("Please enter a product name.")

# Validate URL function
def is_valid_url(url):
    """Validate if the URL is valid"""
    amazon_domains = [
        'amazon.com', 'amazon.co.uk', 'amazon.de', 'amazon.fr', 
        'amazon.it', 'amazon.es', 'amazon.ca', 'amazon.com.au',
        'amazon.co.jp', 'amazon.in', 'amazon.com.br', 'amazon.com.mx'
    ]
    
    # Check for Amazon URLs
    amazon_pattern = r'https?://(www\.)?(' + '|'.join(amazon_domains) + ')/.*/dp/[A-Z0-9]{10}'
    if re.match(amazon_pattern, url):
        return True
    
    # Basic URL validation for other e-commerce sites
    general_pattern = r'https?://[^\s/$.?#].[^\s]*'
    return re.match(general_pattern, url) is not None

# Process URL when fetch button is clicked
if fetch_button:
    if not url_input:
        st.error("Please enter a URL first.")
    elif not is_valid_url(url_input):
        st.error("Please enter a valid product URL.")
    else:
        # Show loading spinner
        with st.spinner("Fetching product details... This may take a few seconds."):
            try:
                # Try to scrape product details
                product_data = scraper.scrape_product(url_input)
                
                if product_data['success']:
                    # Add to wishlist
                    new_item = {
                        'id': str(uuid.uuid4()),
                        'name': product_data['name'] or 'Product name not found',
                        'price': product_data['price'] or 'Price not available',
                        'image_url': product_data['image_url'],
                        'url': url_input,
                        'priority': priority.split()[0],  # Extract High/Medium/Low
                        'date_added': datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
                    }
                    
                    st.session_state.wishlist.append(new_item)
                    st.success(f"Added '{new_item['name'][:50]}...' to your wishlist!")
                    st.rerun()
                
                else:
                    st.error(f"Could not fetch product details: {product_data['error']}")
                    st.info("You can still add this product manually using the form below.")
                    
            except Exception as e:
                st.error(f"An error occurred while fetching: {str(e)}")
                st.info("You can still add this product manually using the form below.")

def display_product_card(item):
    """Display product as a card"""
    priority_class = f"{item['priority'].lower()}-priority"
    
    with st.container():
        # Card container
        col1, col2 = st.columns([1, 2])
        
        with col1:
            # Product image
            if item['image_url']:
                try:
                    response = requests.get(item['image_url'], timeout=10)
                    if response.status_code == 200:
                        image = Image.open(BytesIO(response.content))
                        st.image(image, width=150)
                    else:
                        st.image("https://via.placeholder.com/150x150?text=No+Image", width=150)
                except:
                    st.image("https://via.placeholder.com/150x150?text=No+Image", width=150)
            else:
                st.image("https://via.placeholder.com/150x150?text=No+Image", width=150)
        
        with col2:
            # Priority badge
            priority_colors = {
                "High": "🔴",
                "Medium": "🟡", 
                "Low": "🟢"
            }
            st.markdown(f"{priority_colors.get(item['priority'], '⚪')} **{item['priority']} Priority**")
            
            # Product name
            st.markdown(f"**{item['name'][:80]}{'...' if len(item['name']) > 80 else ''}**")
            
            # Price
            st.markdown(f"💰 **{item['price']}**")
            
            # Date added
            st.caption(f"Added: {item['date_added']}")
            
            # Actions
            col_action1, col_action2, col_action3 = st.columns(3)
            
            with col_action1:
                if item['url']:
                    st.link_button("View", item['url'], use_container_width=True)
                else:
                    st.button("View", disabled=True, use_container_width=True)
            
            with col_action2:
                if st.button("Edit", key=f"edit_{item['id']}", use_container_width=True):
                    # Edit functionality (placeholder)
                    st.info("Edit functionality coming soon!")
            
            with col_action3:
                if st.button("Remove", key=f"remove_{item['id']}", use_container_width=True):
                    st.session_state.wishlist = [i for i in st.session_state.wishlist if i['id'] != item['id']]
                    st.rerun()
        
        st.markdown("---")

def display_product_list_item(item):
    """Display product as a list item"""
    col1, col2, col3, col4 = st.columns([1, 4, 2, 2])
    
    with col1:
        # Small product image
        if item['image_url']:
            try:
                response = requests.get(item['image_url'], timeout=10)
                if response.status_code == 200:
                    image = Image.open(BytesIO(response.content))
                    st.image(image, width=80)
                else:
                    st.image("https://via.placeholder.com/80x80?text=No+Image", width=80)
            except:
                st.image("https://via.placeholder.com/80x80?text=No+Image", width=80)
        else:
            st.image("https://via.placeholder.com/80x80?text=No+Image", width=80)
    
    with col2:
        priority_colors = {"High": "🔴", "Medium": "🟡", "Low": "🟢"}
        st.markdown(f"{priority_colors.get(item['priority'], '⚪')} **{item['name'][:100]}{'...' if len(item['name']) > 100 else ''}**")
        st.caption(f"Added: {item['date_added']}")
    
    with col3:
        st.markdown(f"**{item['price']}**")
    
    with col4:
        if item['url']:
            st.link_button("View", item['url'], use_container_width=True)
        if st.button("Remove", key=f"remove_list_{item['id']}", use_container_width=True):
            st.session_state.wishlist = [i for i in st.session_state.wishlist if i['id'] != item['id']]
            st.rerun()
    
    st.markdown("---")

# Display Wishlist
st.markdown("## Your Wishlist")

if not st.session_state.wishlist:
    st.info("🛍️ Your wishlist is empty. Add some products using the form above!")
else:
    # Filter and search logic
    filtered_items = st.session_state.wishlist.copy()
    
    # Apply search filter
    if search_query:
        filtered_items = [item for item in filtered_items if 
                         search_query.lower() in item['name'].lower()]
    
    # Apply priority filter
    if filter_priority != "All Items":
        priority_key = filter_priority.split()[0]  # Extract High/Medium/Low
        filtered_items = [item for item in filtered_items if 
                         item['priority'] == priority_key]
    
    # Apply sorting
    if sort_by == "Date Added":
        filtered_items.sort(key=lambda x: x['date_added'], reverse=True)
    elif sort_by == "Priority":
        priority_order = {"High": 3, "Medium": 2, "Low": 1}
        filtered_items.sort(key=lambda x: priority_order.get(x['priority'], 0), reverse=True)
    
    if not filtered_items:
        st.info("No items match your search criteria.")
    else:
        # Display items
        if view_mode == "🔲":  # Grid view
            cols = st.columns(3)
            for idx, item in enumerate(filtered_items):
                with cols[idx % 3]:
                    display_product_card(item)
        else:  # List view
            for item in filtered_items:
                display_product_list_item(item)

# Footer
st.markdown("---")
st.markdown("*🛍️ Curate your perfect collection of desired items from any e-commerce website.*")
